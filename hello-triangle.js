import { Vec2 } from "planck";
import GridMap from "./grid-map.js";
import forceMapData from "./data.json" with { type: "json" };
import Teleporter, { TeleporterComponent } from "./teleporter.js";
import Color from "./color.js";
import Particle, { MouseParticle } from "./particle.js";
import ForceMap from "./force-map.js";

const sampleName = "sample4";

// Vertex Shader
const vertexShaderSource = `
  attribute vec2 aPosition;
  attribute vec2 aTranslation;
  attribute float aRadius;
  attribute vec4 aColor;

  uniform vec2 uResolution;

  varying vec4 vColor;

  void main() {
      vec2 scaledPosition = aPosition * aRadius;
      vec2 position = scaledPosition + aTranslation;
      vec2 zeroToOne = position / uResolution;
      vec2 zeroToTwo = zeroToOne * 2.0;
      vec2 clipSpace = zeroToTwo - 1.0;
      gl_Position = vec4(clipSpace * vec2(1, -1), 0, 1);
      vColor = aColor;
  }

`;

// Fragment Shader
const fragmentShaderSource = `
  precision mediump float;
  varying vec4 vColor;
  void main() {
      gl_FragColor = vColor;
  }
`;

const meterPixelRatio = 100;
globalThis.meterPixelRatio = meterPixelRatio;

class ParticleSystem {

  static colors = {
    red: Color.from({ "r": 0.95, "g": 0.26, "b": 0.21 }, false),
    green: Color.from({ "r": 0.30, "g": 0.69, "b": 0.31 }, false),
    blue: Color.from({ "r": 0.13, "g": 0.59, "b": 0.95 }, false),
    yellow: Color.from({ "r": 1.00, "g": 0.92, "b": 0.23 }, false),
    // cyan: new Color(0, 1, 1),
    // magenta: new Color(1, 0, 1),
    // orange: new Color(1, 0.5, 0),
    // purple: new Color(0.5, 0, 0.5),
    pink: Color.from({ "r": 0.81, "g": 0.12, "b": 0.49 }, false),
    // lime: new Color(0.75, 1, 0),
    // teal: new Color(0, 0.5, 0.5),
    // brown: new Color(0.6, 0.3, 0.1),
    // black: new Color(0, 0, 0),
    // white: new Color(1, 1, 1)
  };

  forceMap = sampleName ?
    ForceMap.from(Color, forceMapData[sampleName])
    : new ForceMap(Object.values(ParticleSystem.colors));

  /** @type {GridMap<Particle | TeleporterComponent>} */
  gridMap = new GridMap(16, 16);

  /**
   * @param {WebGL2RenderingContext} gl
   * @param {{ width: number; height: number }} resolution
   * @param {Particle[]} particles
   * @param {Teleporter[]} teleporters
   */
  constructor(gl, resolution, particles, teleporters) {
    this.gl = gl;
    this.resolution = resolution;
    this.particles = particles;
    this.teleporters = teleporters;
    this.numVertices = 6;
    for(const particle of this.particles) {
      particle.particleSystem = this;
      particle.gridMapPosition[0] = this.xToGridMapRow(particle.position.x);
      particle.gridMapPosition[1] = this.yToGridMapRow(particle.position.y);
      this.gridMap.add(...particle.gridMapPosition, particle);
    }
    for(const teleporter of this.teleporters) {
      teleporter.particleSystem = this;
      teleporter.inComponent.gridMapPosition[0] = this.xToGridMapRow(teleporter.inComponent.position.x);
      teleporter.inComponent.gridMapPosition[1] = this.yToGridMapRow(teleporter.inComponent.position.y);
      teleporter.outComponent.gridMapPosition[0] = this.xToGridMapRow(teleporter.outComponent.position.x);
      teleporter.outComponent.gridMapPosition[1] = this.yToGridMapRow(teleporter.outComponent.position.y);
      this.gridMap.add(...teleporter.inComponent.gridMapPosition, teleporter.inComponent);
      this.gridMap.add(...teleporter.outComponent.gridMapPosition, teleporter.outComponent);
    }
    this.init();
  }

  /** @param {number} x */
  xToGridMapRow(x) {
    return Math.floor(x / (this.resolution.width / meterPixelRatio) * this.gridMap.rows);
  }

  /** @param {number} y */
  yToGridMapRow(y) {
    return Math.floor(y / (this.resolution.height / meterPixelRatio) * this.gridMap.columns);
  }

  init() {
    const gl = this.gl;

    // Compile shaders
    this.program = this.createProgram(gl, vertexShaderSource, fragmentShaderSource);
    gl.useProgram(this.program);

    // Get attribute and uniform locations
    this.positionLocation = gl.getAttribLocation(this.program, 'aPosition');
    this.translationLocation = gl.getAttribLocation(this.program, 'aTranslation');
    this.radiusLocation = gl.getAttribLocation(this.program, 'aRadius');
    this.colorLocation = gl.getAttribLocation(this.program, 'aColor');
    this.resolutionLocation = gl.getUniformLocation(this.program, 'uResolution');

    // Create a buffer and put a unit circle in it
    const circleVertices = this.createCircleVertices();

    this.positionBuffer = gl.createBuffer();
    gl.bindBuffer(gl.ARRAY_BUFFER, this.positionBuffer);
    gl.bufferData(gl.ARRAY_BUFFER, new Float32Array(circleVertices), gl.STATIC_DRAW);

    // Create buffers for instanced attributes
    this.translationBuffer = gl.createBuffer();
    this.radiusBuffer = gl.createBuffer();
    this.colorBuffer = gl.createBuffer();

    this.updateBuffers();

    // Set resolution uniform once as it does not change
    gl.uniform2f(this.resolutionLocation, this.resolution.width, this.resolution.height);
  }

  createProgram(gl, vertexShaderSource, fragmentShaderSource) {
    const vertexShader = this.createShader(gl, gl.VERTEX_SHADER, vertexShaderSource);
    const fragmentShader = this.createShader(gl, gl.FRAGMENT_SHADER, fragmentShaderSource);
    const program = gl.createProgram();
    gl.attachShader(program, vertexShader);
    gl.attachShader(program, fragmentShader);
    gl.linkProgram(program);
    if (!gl.getProgramParameter(program, gl.LINK_STATUS)) {
      console.error('Program failed to link:', gl.getProgramInfoLog(program));
      gl.deleteProgram(program);
      return null;
    }
    return program;
  }

  createShader(gl, type, source) {
    const shader = gl.createShader(type);
    gl.shaderSource(shader, source);
    gl.compileShader(shader);
    if (!gl.getShaderParameter(shader, gl.COMPILE_STATUS)) {
      console.error('Shader compilation failed:', gl.getShaderInfoLog(shader));
      gl.deleteShader(shader);
      return null;
    }
    return shader;
  }

  createCircleVertices() {
    const vertices = [];
    for (let i = 0; i <= this.numVertices; i++) {
      const angle = (i / this.numVertices) * Math.PI * 2;
      vertices.push(Math.cos(angle), Math.sin(angle));
    }
    return vertices;
  }

  updateBuffers() {
    const gl = this.gl;
    const translations = [];
    const radii = [];
    const colors = [];

    for(const teleporter of this.teleporters) {
      translations.push(
        teleporter.inComponent.position.x * meterPixelRatio,
        teleporter.inComponent.position.y * meterPixelRatio
      );
      radii.push(teleporter.inComponent.radius * meterPixelRatio);
      colors.push(...teleporter.inComponent.color);
      translations.push(
        teleporter.outComponent.position.x * meterPixelRatio,
        teleporter.outComponent.position.y * meterPixelRatio
      );
      radii.push(teleporter.outComponent.radius * meterPixelRatio);
      colors.push(...teleporter.outComponent.color);
    }
    for(const particle of this.particles) {
      // if(particle.isAlpha) {
      //   translations.push(particle.position.x * meterPixelRatio, particle.position.y * meterPixelRatio);
      //   radii.push(particle.radius * meterPixelRatio * 10);
      //   colors.push(0.2, 0.1, 0.1, 0);
      // }
      translations.push(particle.position.x * meterPixelRatio, particle.position.y * meterPixelRatio);
      radii.push(particle.radius * meterPixelRatio);
      colors.push(particle.color.r, particle.color.g, particle.color.b, particle.color.a);
    }

    gl.bindBuffer(gl.ARRAY_BUFFER, this.translationBuffer);
    gl.bufferData(gl.ARRAY_BUFFER, new Float32Array(translations), gl.DYNAMIC_DRAW);

    gl.bindBuffer(gl.ARRAY_BUFFER, this.radiusBuffer);
    gl.bufferData(gl.ARRAY_BUFFER, new Float32Array(radii), gl.DYNAMIC_DRAW);

    gl.bindBuffer(gl.ARRAY_BUFFER, this.colorBuffer);
    gl.bufferData(gl.ARRAY_BUFFER, new Float32Array(colors), gl.DYNAMIC_DRAW);
  }

  #counter = 0;
  #processParticlesPerFrame = 3200;

  update(deltaTime) {
    const rMax = 0.75;
    // for(const particleA of this.particles) {
    //   particleA.acceleration.setZero();
    //   for(const particleB of this.particles) {
    //     if(particleA === particleB) {
    //       continue;
    //     }
    //     const distance = Vec2.distance(particleA.position, particleB.position);
    //     const forceFactor = this.forceMap.get(particleA.color, particleB.color) ?? 0;
    //     const force = this.force(distance / rMax, forceFactor);
    //     const direction = Vec2.sub(particleA.position, particleB.position).mul(1 / distance);
    //     if(force < 0) {
    //       // debugger;
    //     }
    //     particleA.acceleration.add(direction.mul(force * -1 * 2));
    //   }
    //   particleA.acceleration.mul(rMax);
    //   // debugger;
    // }
    const gridWidth = this.resolution.width / meterPixelRatio;
    const gridHeight = this.resolution.height / meterPixelRatio;
    let iterations = 0
    for(let p = 0; p < this.#processParticlesPerFrame; p++) {
      const index = p + this.#counter * this.#processParticlesPerFrame;
      const particleA = this.particles[index % this.particles.length];
    // for(const particleA of this.gridMap.cell(this.#counter, 16 * 16 / 4)) {
      particleA.acceleration.setZero();
      for(const [row, column, particleB, wrapDirection] of this.gridMap.valuesInRange(...particleA.gridMapPosition, 2)) {
        // console.log(particleA.gridMapPosition, row, column, particleB)
        if(particleA === particleB) {
          continue;
        }
        let positionOfB = particleB.position.clone();
        if(wrapDirection.x < 0) {
          positionOfB.x -= gridWidth;
        } else if(wrapDirection.x > 0) {
          positionOfB.x += gridWidth;
        }
        if(wrapDirection.y < 0) {
          positionOfB.y -= gridHeight;
        } else if(wrapDirection.y > 0) {
          positionOfB.y += gridHeight;
        }
        const distance = Vec2.distance(particleA.position, positionOfB);

        // Prevent particles from getting too close - add minimum distance threshold
        const minDistance = 0.01; // Minimum distance to prevent division by zero and extreme forces
        const effectiveDistance = Math.max(distance, minDistance);

        const direction = effectiveDistance === 0 ? Vec2.zero() : Vec2.sub(particleA.position, positionOfB).mul(1 / effectiveDistance);
        let force;
        if(particleB instanceof TeleporterComponent) {
          force = particleB.teleporter.handleParticle(particleB, particleA, effectiveDistance, direction);
        } else {
          const forceFactor = this.forceMap.get(particleA.actualColor, particleB.actualColor) ?? 0;
          force = this.force(effectiveDistance / rMax, forceFactor);

          // Clamp force to prevent explosive behavior
          const maxForce = 10.0; // Maximum force magnitude
          force = Math.max(-maxForce, Math.min(maxForce, force));
        }
        if(force < 0) {
          // debugger;
        }
        particleA.acceleration.add(direction.mul(-force));
        // if(Number.isNaN(particleA.acceleration.x)) {
        //   console.log(distance, -force * distance * 20, direction.mul);
        //   throw "ERR";
        // }
        iterations++;
      }
      particleA.acceleration.mul(rMax);
    }
    this.#counter++;
    // if(this.#counter >= this.gridMap.rows * this.gridMap.columns) {
    //   this.#counter = 0;
    // }
    if(this.#counter * this.#processParticlesPerFrame >= this.particles.length) {
      this.#counter = 0;
    }
    for(const teleporter of this.teleporters) {
      teleporter.update(deltaTime);
    }
    for(const particle of this.particles) {
      particle.update(deltaTime);
      const [ oldRow, oldColumn ] = particle.gridMapPosition;
      particle.gridMapPosition[0] = this.xToGridMapRow(particle.position.x);
      particle.gridMapPosition[1] = this.yToGridMapRow(particle.position.y);
      this.gridMap.move(oldRow, oldColumn, ...particle.gridMapPosition, particle);
    }
    this.updateBuffers();
  }

  #beta = 0.3;

  force(r, a) {
    // Ensure r is never negative or too small to prevent extreme forces
    r = Math.max(0.001, r);

    if(r < this.#beta) {
      // Repulsive force when particles are very close
      // Use a smoother function to prevent explosive behavior
      const repulsion = r / this.#beta - 1;
      return Math.max(-5.0, repulsion); // Clamp maximum repulsion
    }
    if(this.#beta < r && r < 1) {
      // Attractive/repulsive force based on force factor 'a'
      return a * (1 - Math.abs(2 * r - 1 - this.#beta) / (1 - this.#beta));
    }
    return 0;
  }

  draw() {
    /** @type {WebGL2RenderingContext} */
    const gl = this.gl;

    gl.useProgram(this.program);

    // Bind circle vertices
    gl.bindBuffer(gl.ARRAY_BUFFER, this.positionBuffer);
    gl.enableVertexAttribArray(this.positionLocation);
    gl.vertexAttribPointer(this.positionLocation, 2, gl.FLOAT, false, 0, 0);

    // Bind translations
    gl.bindBuffer(gl.ARRAY_BUFFER, this.translationBuffer);
    gl.enableVertexAttribArray(this.translationLocation);
    gl.vertexAttribPointer(this.translationLocation, 2, gl.FLOAT, false, 0, 0);
    gl.vertexAttribDivisor(this.translationLocation, 1);

    // Bind radii
    gl.bindBuffer(gl.ARRAY_BUFFER, this.radiusBuffer);
    gl.enableVertexAttribArray(this.radiusLocation);
    gl.vertexAttribPointer(this.radiusLocation, 1, gl.FLOAT, false, 0, 0);
    gl.vertexAttribDivisor(this.radiusLocation, 1);

    // Bind colors
    gl.bindBuffer(gl.ARRAY_BUFFER, this.colorBuffer);
    gl.enableVertexAttribArray(this.colorLocation);
    gl.vertexAttribPointer(this.colorLocation, 4, gl.FLOAT, false, 0, 0);
    gl.vertexAttribDivisor(this.colorLocation, 1);

    // Draw
    const numInstances = this.particles.length + this.teleporters.length * 2;
    gl.drawArraysInstanced(gl.TRIANGLE_FAN, 0, this.numVertices, numInstances);
  }
}

globalThis.colors = sampleName ?
  forceMapData[sampleName].map(entry => Color.from(entry[0], false)) :
  Object.values(ParticleSystem.colors);

function setupWebGL() {
  /** @type {HTMLCanvasElement} */
  const canvas = document.getElementById('demo-canvas');
  canvas.width = window.innerWidth;
  canvas.height = window.innerHeight;
  const gl = canvas.getContext('webgl2');

  if(gl === null) {
    console.error('WebGL not supported');
    return;
  }

  gl.enable(gl.BLEND);
  gl.blendFunc(gl.SRC_ALPHA, gl.ONE_MINUS_SRC_ALPHA);

  const resolution = { width: canvas.width, height: canvas.height };
  const radius = 2 / meterPixelRatio;

  const mouseParticle = new MouseParticle({ x: resolution.width / 2, y: resolution.height / 2 });

  const particles = [ mouseParticle ];
  for (let i = 1; i < 3000; i++) {
      const position = {
          x: (Math.random() * resolution.width * 0.9 + resolution.width * 0.05) / meterPixelRatio,
          y: (Math.random() * resolution.height * 0.9 + resolution.height * 0.05) / meterPixelRatio
      };
      const velocity = {
          x: (Math.random() - 0.5) / meterPixelRatio,
          y: (Math.random() - 0.5) / meterPixelRatio
      };
      const color = colors[Math.floor(Math.random() * colors.length)];
      particles.push(new Particle(position, velocity, radius, color));
  }

  const teleporters = [];
  for(let i = 0; i < 2; i++) {
    teleporters.push(
      new Teleporter(
        new TeleporterComponent(
          4,
          { x: resolution.width * Math.random() / meterPixelRatio, y: resolution.height / meterPixelRatio * Math.random() },
          (Math.floor(Math.random() * 40) + 30) / meterPixelRatio,
          new Color(0.1, 0.1, 0.2, 0.25)
        ),
        new TeleporterComponent(
          -4,
          { x: resolution.width * Math.random() / meterPixelRatio, y: resolution.height / meterPixelRatio * Math.random() },
          (Math.floor(Math.random() * 40) + 30) / meterPixelRatio,
          new Color(0.2, 0.1, 0.1, 0.25)
        )
      )
    );
  }

  const particleSystem = new ParticleSystem(gl, resolution, particles, teleporters);
  globalThis.particleSystem = particleSystem;
  particleSystem.forceMap.setInteractor(mouseParticle.actualColor, -1);

  let requestAnimationFrameId;
  let lastFrameTime = Date.now();

  function update() {
    const currentTime = Date.now();
    const deltaMs = currentTime - lastFrameTime
    const deltaTime = deltaMs / 1000;
    lastFrameTime = currentTime;

    output.value = Math.floor(1000 / deltaMs);
    particleSystem.update(100 / 1000);
    // particleSystem.update(Math.max(16 / 1000, deltaTime));
    setTimeout(update, 16);
  }

  function render() {
      // console.log(output.value)
      gl.clear(gl.COLOR_BUFFER_BIT);

      particleSystem.draw();

      requestAnimationFrameId = requestAnimationFrame(render);
  }

  function pauseRendering() {
    cancelAnimationFrame(requestAnimationFrameId);
    requestAnimationFrameId = undefined;
  }

  function resumeRendering() {
    lastFrameTime = Date.now();
    render();
  }

  function toggleRendering() {
    if(requestAnimationFrameId === undefined) {
      resumeRendering();
      return true;
    }
    pauseRendering();
    return false;
  }

  window.addEventListener("keydown", function(event) {
    switch(event.code) {
      case "Space": toggleRendering();
    }
  });

  gl.clearColor(0, 0, 0, 0);
  update();
  render();
}

setupWebGL();

