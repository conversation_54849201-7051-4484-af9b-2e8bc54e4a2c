import { Vec2 } from "planck";
import Color from "./color.js";

export default class Particle {

  static #hasAlpha = false;

  /** @type {[ row: number, column: number ]} */
  gridMapPosition = [];
  /** @type {ParticleSystem} */
  particleSystem;

  /**
   * @param {import("planck").Vec2Value} position
   * @param {import("planck").Vec2Value} velocity
   * @param {number} radius
   * @param {Color} color
   */
  constructor(position, velocity, radius, color) {
    this.isAlpha = !Particle.#hasAlpha;
    Particle.#hasAlpha ||= true;
    this.position = new Vec2(position);
    this.velocity = new Vec2(velocity);
    this.acceleration = Vec2.zero();
    this.radius = radius;
    this.actualColor = color;
    this.color = this.actualColor.copy();
  }

  /** @param {number} deltaTime */
  update(deltaTime) {
    // const mu = 0.40;
    // this.velocity.mul(Math.pow(0.5, deltaTime / mu));
    this.velocity.mul(0.45);
    this.velocity.x += this.acceleration.x * deltaTime;
    this.velocity.y += this.acceleration.y * deltaTime;

    // Optimized velocity clamping - calculate speed once
    const vx = this.velocity.x;
    const vy = this.velocity.y;
    const speedSquared = vx * vx + vy * vy;
    const maxSpeed = 5.0;
    const maxSpeedSquared = maxSpeed * maxSpeed;

    if(speedSquared > maxSpeedSquared) {
      const scale = maxSpeed / Math.sqrt(speedSquared);
      this.velocity.x *= scale;
      this.velocity.y *= scale;
    }

    this.position.x += this.velocity.x * deltaTime;
    this.position.y += this.velocity.y * deltaTime;
    // this.position.x += Math.sin(this.position.y * 2) / 100
    // this.position.y += Math.cos(this.position.x * 2) / 100

    // Use already calculated speed for visual effects
    const speed = speedSquared > maxSpeedSquared ? maxSpeed : Math.sqrt(speedSquared);
    if(speed > 2) {
      // this.actualColor = colors[4];
    }
    const clampSpeed = (Math.min(speed, 1) + 0.275) / meterPixelRatio;
    this.radius = clampSpeed * 4.5 + 0.01;
    const oneMinusSpeed = clampSpeed * 30;
    this.color.r = Math.min(1, this.actualColor.r + oneMinusSpeed);
    this.color.g = Math.min(1, this.actualColor.g + oneMinusSpeed);
    this.color.b = Math.min(1, this.actualColor.b + oneMinusSpeed);
    this.color.a =  Math.min(1, this.actualColor.a - oneMinusSpeed);
    // console.log(clampSpeed)
    // this.color.a = Math.min(1, 1 / clampSpeed);
    this.wrap();
  }

  // Wrap around the edges of the viewport
  wrap() {
    const { resolution } = this.particleSystem;
    const position = Vec2.mulVec2Num(this.position, meterPixelRatio);
    if(position.x < 0) position.x += resolution.width - 1;
    if(position.x > resolution.width) position.x -= resolution.width - 1;
    if(position.y < 0) position.y += resolution.height - 1;
    if(position.y > resolution.height) position.y -= resolution.height - 1;
    this.position.set(position.mul(1 / meterPixelRatio));
  }

};

export class MouseParticle extends Particle {

  /** @param {import("planck").Vec2Value} position */
  constructor(position) {
    super(position, Vec2.zero(), 4 / meterPixelRatio, new Color(0, 0, 0, 1));
    window.addEventListener("mousemove", event => {
      this.position.set(event).mul(1 / meterPixelRatio);
    });
    window.addEventListener("mousedown", event => {
      let force;
      switch(event.button) {
        case 0: force = 10;
        break;
        case 2: force = -50;
      }
      this.particleSystem?.forceMap.setInteractor(this.actualColor, force);
    });
    window.addEventListener("mouseup", () => {
      this.particleSystem?.forceMap.setInteractor(this.actualColor, -1);
    });
    window.addEventListener("contextmenu", event => event.preventDefault());
  }

  /** @param {number} deltaTime */
  update(deltaTime) {
    this.color.r = this.actualColor.r;
    this.color.g = this.actualColor.g;
    this.color.g = this.actualColor.b;
    this.color.a = this.actualColor.a;
    // console.log(this.color);
    // debugger
    // super.update(delta, resolution);
    super.wrap();
  }

};